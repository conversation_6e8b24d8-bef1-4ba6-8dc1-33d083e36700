"""
VpTransformer - Improved Sonic Velocity Prediction Package

This package provides improved VpTransformer models for predicting sonic velocity (Vp)
from well log curves with proper scaling and enhanced architecture.

Core Components:
- VpTransformer: Main transformer model with improved VpDecoder
- VpDataNormalizer: Enhanced data normalization with proper scaling
- VpPredictor: Integration wrapper for easy usage
- LASProcessor: Data preprocessing utilities

Key Improvements (2025):
- Fixed sigmoid activation constraints that limited predictions
- Proper scaling for full [40-400] μs/ft range without artificial bounds
- Enhanced normalization approach with curve-specific methods
- Data leakage prevention and training/testing independence
- Better gradient flow and model architecture
"""

from .vp_model_improved import (
    VpTransformer,
    VpDecoder,
    MWLT_Vp_Small,
    MWLT_Vp_Base, 
    MWLT_Vp_Large,
    VpDataNormalizer,
    VpLoss,
    VpDataset,
    create_improved_vp_data,
    find_data_files
)

from .model import (
    Input_Embedding,
    ResCNN,
    PositionalEncoding,
    TransformerEncoder,
    TransformerBlock,
    SelfAtten<PERSON>,
    <PERSON>ed<PERSON>or<PERSON>,
    Decoder,
    MWL_Transformer,
    MWLT_Small,
    MWLT_Base,
    MWLT_Large
)

from .utils import (
    get_device,
    save_checkpoint,
    load_checkpoint,
    EarlyStopping,
    cal_RMSE,
    cal_R2
)

from .las_processor import (
    LASProcessor,
    create_test_las_file
)

# Import the integration wrapper if it exists
try:
    from .predictor import VpPredictor, VpTransformerAPI
except ImportError:
    # predictor.py not yet created
    pass

# Package metadata
__version__ = "1.0.0"
__author__ = "VpTransformer Team"
__description__ = "Improved VpTransformer for sonic velocity prediction from well logs"

# Key model configurations
MODEL_CONFIG = {
    'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
    'output_curve': 'VP',
    'sequence_length': 640,
    'model_variants': {
        'small': {'res_blocks': 2, 'encoders': 2, 'heads': 2, 'features': 64},
        'base': {'res_blocks': 4, 'encoders': 4, 'heads': 4, 'features': 64},
        'large': {'res_blocks': 6, 'encoders': 6, 'heads': 8, 'features': 128}
    },
    'vp_range': (40, 400),  # μs/ft - proper scaling without artificial bounds
    'device_preference': 'auto',  # 'auto', 'gpu', 'cpu'
    'improvements': {
        'sigmoid_fix': True,           # Fixed sigmoid activation issue
        'proper_normalization': True,  # Enhanced normalization approach
        'data_leakage_prevented': True, # Verified training/testing independence
        'enhanced_decoder': True       # Improved VpDecoder architecture
    },
    'training_params': {
        'learning_rate': 1e-4,
        'batch_size': 8,
        'epochs': 200,
        'patience': 50
    }
}

# Essential dependencies
REQUIRED_DEPENDENCIES = [
    'torch>=1.8.0',
    'numpy>=1.19.0', 
    'h5py>=3.1.0',
    'scikit-learn>=0.24.0'
]

# Optional dependencies for enhanced features
OPTIONAL_DEPENDENCIES = [
    'matplotlib>=3.3.0',  # Plotting and visualization
    'pandas>=1.2.0',      # Data analysis
    'lasio>=0.30',        # LAS file reading
    'thop>=0.0.31'        # Model profiling
]

# Quick access functions
def get_model(model_type='base', **kwargs):
    """
    Get a pre-configured VpTransformer model
    
    Args:
        model_type: 'small', 'base', or 'large'
        **kwargs: Additional model parameters
        
    Returns:
        VpTransformer model
    """
    if model_type == 'small':
        return MWLT_Vp_Small(**kwargs)
    elif model_type == 'base':
        return MWLT_Vp_Base(**kwargs)
    elif model_type == 'large':
        return MWLT_Vp_Large(**kwargs)
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def get_normalizer():
    """Get a VpDataNormalizer instance"""
    return VpDataNormalizer()

def get_processor():
    """Get a LASProcessor instance"""
    return LASProcessor()

# Package information
def get_package_info():
    """Return comprehensive package information"""
    return {
        'version': __version__,
        'description': __description__,
        'author': __author__,
        'improvements': MODEL_CONFIG['improvements'],
        'model_variants': MODEL_CONFIG['model_variants'],
        'vp_range': MODEL_CONFIG['vp_range'],
        'required_deps': REQUIRED_DEPENDENCIES,
        'optional_deps': OPTIONAL_DEPENDENCIES
    }

# Compatibility aliases
VpModel = VpTransformer  # Alias for backward compatibility
VpNormalizer = VpDataNormalizer  # Shorter alias

__all__ = [
    # Core models
    'VpTransformer', 'VpDecoder', 'VpModel',
    'MWLT_Vp_Small', 'MWLT_Vp_Base', 'MWLT_Vp_Large',
    
    # Base components
    'Input_Embedding', 'ResCNN', 'PositionalEncoding',
    'TransformerEncoder', 'TransformerBlock', 'SelfAttention',
    'FeedForward', 'Decoder', 'MWL_Transformer',
    'MWLT_Small', 'MWLT_Base', 'MWLT_Large',
    
    # Data processing
    'VpDataNormalizer', 'VpNormalizer', 'VpLoss', 'VpDataset',
    'LASProcessor', 'create_test_las_file',
    
    # Utilities
    'get_device', 'save_checkpoint', 'load_checkpoint',
    'EarlyStopping', 'cal_RMSE', 'cal_R2',
    
    # Data utilities
    'create_improved_vp_data', 'find_data_files',
    
    # Quick access functions
    'get_model', 'get_normalizer', 'get_processor',
    'get_package_info',
    
    # Configuration
    'MODEL_CONFIG', 'REQUIRED_DEPENDENCIES', 'OPTIONAL_DEPENDENCIES',
    
    # Metadata
    '__version__', '__author__', '__description__'
]