"""
Train and Test VpTransformer Example
Demonstrates how to train the VpTransformer model and then test it with real data

This script shows:
1. How to train the VpTransformer using the built-in training function
2. How to load the trained model for inference
3. How to make predictions with trained weights
4. Comparison between untrained and trained model performance
"""
import os
import sys
import numpy as np
import h5py
import matplotlib.pyplot as plt
import torch

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import create_improved_vp_data, VpDataNormalizer, MWLT_Vp_Base
    from vp_predictor.utils import get_device
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    sys.exit(1)

def train_vp_model():
    """Train the VpTransformer model using the built-in training function"""
    print("🏋️ TRAINING VpTransformer MODEL")
    print("="*60)
    print("This will create training data and train the model...")
    print("Note: This may take several minutes depending on your hardware")
    
    try:
        # Train the model using the built-in function
        print("\n🔧 Starting training process...")
        result = create_improved_vp_data()
        
        if result:
            print("✅ Training completed successfully!")
            print("📁 Check the generated files:")
            print("   - Model checkpoints (.pth files)")
            print("   - Training logs and plots")
            print("   - Validation results")
            return True
        else:
            print("❌ Training failed or was interrupted")
            return False
            
    except Exception as e:
        print(f"❌ Training error: {e}")
        return False

def load_trained_model(model_path):
    """Load a trained VpTransformer model"""
    print(f"\n📂 Loading trained model from: {model_path}")
    
    try:
        device = get_device(device_id=0)
        
        # Create model architecture
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        
        # Load trained weights
        checkpoint = torch.load(model_path, map_location=device)
        
        # Handle different checkpoint formats
        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"   📊 Epoch: {checkpoint.get('epoch', 'Unknown')}")
                print(f"   📉 Loss: {checkpoint.get('loss', 'Unknown')}")
            elif 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model.load_state_dict(checkpoint)
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        print("✅ Model loaded successfully!")
        return model, device
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None

def test_with_real_data(model, device, file_path, well_name):
    """Test the trained model with real well log data"""
    print(f"\n🧪 Testing trained model with {well_name}")
    print("-" * 40)
    
    try:
        # Load and prepare data
        curves = {}
        with h5py.File(file_path, 'r') as f:
            for curve_name in f.keys():
                data = f[curve_name][:]
                if data.ndim > 1:
                    data = data.squeeze()
                curves[curve_name] = data
        
        # Prepare input data
        input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        target_curve = 'AC'
        sequence_length = 640
        
        input_features = []
        for curve_name in input_curves:
            if curve_name in curves:
                data = resample_curve(curves[curve_name], sequence_length)
                input_features.append(data)
        
        target_data = resample_curve(curves[target_curve], sequence_length)
        input_array = np.array(input_features)
        
        # Normalize data (important for trained model!)
        normalizer = VpDataNormalizer()
        
        # Normalize input curves
        normalized_input = []
        for i, curve_name in enumerate(input_curves):
            if curve_name in normalizer.input_stats:
                stats = normalizer.input_stats[curve_name]
                normalized = (input_array[i] - stats['mean']) / stats['std']
                normalized_input.append(normalized)
            else:
                print(f"⚠️  No normalization stats for {curve_name}")
                normalized_input.append(input_array[i])
        
        normalized_input = np.array(normalized_input)
        
        # Make prediction
        with torch.no_grad():
            input_tensor = torch.FloatTensor(normalized_input).unsqueeze(0).to(device)
            normalized_prediction = model(input_tensor)
            normalized_prediction_np = normalized_prediction.squeeze().cpu().numpy()
        
        # Denormalize prediction
        vp_stats = normalizer.vp_stats
        prediction = normalized_prediction_np * vp_stats['std'] + vp_stats['mean']
        
        print(f"   📊 Input shape: {input_array.shape}")
        print(f"   🎯 Target range: [{target_data.min():.1f}, {target_data.max():.1f}] μs/ft")
        print(f"   🔮 Prediction range: [{prediction.min():.1f}, {prediction.max():.1f}] μs/ft")
        
        # Calculate metrics
        error = prediction - target_data
        rmse = np.sqrt(np.mean(error**2))
        mae = np.mean(np.abs(error))
        r2 = 1 - np.sum(error**2) / np.sum((target_data - target_data.mean())**2)
        
        print(f"   📈 RMSE: {rmse:.2f} μs/ft")
        print(f"   📈 MAE: {mae:.2f} μs/ft")
        print(f"   📈 R²: {r2:.3f}")
        
        return {
            'input_data': input_array,
            'target_data': target_data,
            'prediction': prediction,
            'metrics': {'rmse': rmse, 'mae': mae, 'r2': r2}
        }
        
    except Exception as e:
        print(f"❌ Error testing with {well_name}: {e}")
        return None

def resample_curve(data, target_length):
    """Resample curve to target length"""
    if len(data) == target_length:
        return data
    original_indices = np.linspace(0, len(data) - 1, len(data))
    target_indices = np.linspace(0, len(data) - 1, target_length)
    return np.interp(target_indices, original_indices, data)

def find_trained_model():
    """Find the most recent trained model file"""
    # Look for common model file patterns
    possible_paths = [
        "../best_vp_model.pth",
        "../models/best_vp_model.pth",
        "../vp_model_best.pth",
        "../improved_vp_model.pth"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Look for any .pth files in parent directory
    parent_dir = ".."
    if os.path.exists(parent_dir):
        for file in os.listdir(parent_dir):
            if file.endswith('.pth') and 'vp' in file.lower():
                return os.path.join(parent_dir, file)
    
    return None

def main():
    """Main function demonstrating training and testing workflow"""
    print("🚀 TRAIN AND TEST VpTransformer EXAMPLE")
    print("="*60)
    print("Complete workflow: Train → Load → Test with real data")
    
    # Step 1: Check for existing trained model
    trained_model_path = find_trained_model()
    
    if trained_model_path:
        print(f"\n✅ Found existing trained model: {trained_model_path}")
        use_existing = input("Use existing model? (y/n): ").lower().strip()
        
        if use_existing != 'y':
            trained_model_path = None
    
    # Step 2: Train model if needed
    if not trained_model_path:
        print("\n🏋️ No trained model found. Starting training...")
        training_success = train_vp_model()
        
        if training_success:
            # Look for the newly created model
            trained_model_path = find_trained_model()
            if not trained_model_path:
                print("❌ Training completed but model file not found")
                return
        else:
            print("❌ Training failed. Cannot proceed with testing.")
            return
    
    # Step 3: Load trained model
    model, device = load_trained_model(trained_model_path)
    
    if model is None:
        print("❌ Failed to load model. Cannot proceed with testing.")
        return
    
    # Step 4: Test with real data
    test_files = [("A1.hdf5", "A1"), ("A2.hdf5", "A2")]
    results = {}
    
    for file_path, well_name in test_files:
        if os.path.exists(file_path):
            result = test_with_real_data(model, device, file_path, well_name)
            results[well_name] = result
        else:
            print(f"⚠️  Data file not found: {file_path}")
    
    # Step 5: Summary
    print(f"\n" + "="*60)
    print("🎉 TRAIN AND TEST COMPLETED")
    print("="*60)
    
    successful_tests = sum(1 for result in results.values() if result is not None)
    print(f"✅ Successfully tested: {successful_tests}/{len(test_files)} wells")
    
    for well_name, result in results.items():
        if result is not None:
            metrics = result['metrics']
            print(f"✅ {well_name}: RMSE={metrics['rmse']:.1f}, R²={metrics['r2']:.3f}")
        else:
            print(f"❌ {well_name}: Failed")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Analyze the prediction results")
    print(f"2. Fine-tune model hyperparameters if needed")
    print(f"3. Implement GeneralTransformer for multi-curve prediction")
    print(f"4. Deploy model for production use")

if __name__ == "__main__":
    main()
