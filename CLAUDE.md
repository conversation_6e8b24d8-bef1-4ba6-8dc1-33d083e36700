# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **VpTransformer Integration Package** - a production-ready module for incorporating improved VpTransformer models into external applications for sonic velocity (Vp) prediction from well log curves. The package provides complete integration solutions with enhanced architecture, proper scaling, and comprehensive APIs for well log analysis.

## Key Improvements (2025)

- **Fixed Sigmoid Constraints**: Eliminated artificial range limits that restricted predictions to ~40 μs/ft floor
- **Proper Scaling**: Output correctly scaled to realistic Vp range (40-400 μs/ft) without artificial bounds  
- **Enhanced Normalization**: Curve-specific normalization with log-transform for resistivity curves
- **Data Leakage Prevention**: Verified training/testing independence for reliable model evaluation
- **Improved Architecture**: VpDecoder with better activation functions and gradient flow
- **Production APIs**: Clean interfaces with validation, error handling, and confidence scoring

## Core Architecture

### Package Structure
```
vp_predictor/                    # Main integration package
├── __init__.py                  # Package exports and configuration
├── vp_model_improved.py         # 🆕 Improved VpTransformer architecture
├── model.py                     # Base transformer components (ResNet, Attention, etc.)
├── utils.py                     # Device management and training utilities
├── las_processor.py             # Well log data preprocessing
└── predictor.py                 # Integration wrapper classes (if implemented)

models/                          # Trained model checkpoints directory
examples/                        # Usage examples and demonstrations
├── predict_example.py          # Comprehensive API usage examples
requirements.txt                # Package dependencies
```

### Model Components (vp_predictor/model.py)
- **Input_Embedding**: ResNet-based feature extraction with 1D convolutions
- **TransformerBlock**: Multi-head attention encoders with positional encoding
- **SelfAttention**: Multi-head attention mechanism for sequence modeling
- **FeedForward**: MLP layers with GELU activation and dropout
- **Decoder**: Original decoder with sigmoid activation for density prediction

### VpTransformer Architecture (vp_predictor/vp_model_improved.py)
- **VpDecoder**: 🆕 Improved decoder specifically for Vp prediction without artificial constraints
- **VpTransformer**: Complete model with proper scaling: Input curves → ResNet → Transformer → VpDecoder → Vp output
- **VpDataNormalizer**: Enhanced normalization with curve-specific parameters and log-transform for resistivity
- **Model Variants**: Small (2/2/2), Base (4/4/4), Large (6/6/8) with different ResNet/Encoder/Head configurations

## Development Commands

### Installation
```bash
# Minimal installation (core functionality)
pip install torch numpy h5py scikit-learn

# Full installation with enhanced features
pip install -r requirements.txt

# GPU support (adjust CUDA version as needed)
pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu118
```

### Basic Usage
```bash
# Run comprehensive examples
cd examples
python predict_example.py

# Test individual components
cd vp_predictor
python vp_model_improved.py --mode train --model_type base
python las_processor.py  # Test LAS/HDF5 processing
```

### Python API Usage
```python
# Simple prediction interface
from vp_predictor import VpPredictor
predictor = VpPredictor("models/best_vp_model.pth", device_id=0, model_type="base")
vp_pred = predictor.predict_from_curves(gr, cnl, den, rlld)

# Advanced API with validation
from vp_predictor import VpTransformerAPI
api = VpTransformerAPI("models/best_vp_model.pth", device="auto")
result = api.predict(curves_data, format="curves", validate=True)

# Utility components
from vp_predictor import get_model, get_normalizer, get_processor
model = get_model("base")
normalizer = get_normalizer()
processor = get_processor()
```

## Input/Output Specifications

### Input Curves (Required, 640-point sequences)
- **GR**: Gamma Ray (0-200 API units) - shale content indicator
- **CNL**: Neutron Porosity (0-60 %) - hydrogen content/porosity indicator  
- **DEN**: Bulk Density (1.5-3.0 g/cm³) - lithology/porosity indicator
- **RLLD**: Deep Resistivity (0.1-1000 ohm-m) - fluid saturation indicator (log-transformed)

### Output
- **Vp**: Sonic Velocity (40-400 μs/ft) with proper scaling and no artificial bounds

### Data Processing Pipeline
1. **Input Normalization**: Curve-specific normalization with log-transform for RLLD
2. **Architecture Flow**: [B,4,640] → ResNet → [B,640,64] → Transformer → VpDecoder → [B,1,640]
3. **Output Denormalization**: Convert normalized predictions back to physical units (40-400 μs/ft)

## Model Variants and Configuration

| Variant | ResNet Blocks | Transformer Encoders | Attention Heads | Feature Dim | Parameters |
|---------|---------------|---------------------|-----------------|-------------|------------|
| **Small** | 2 | 2 | 2 | 64 | ~1.2M |
| **Base** | 4 | 4 | 4 | 64 | ~2.1M |
| **Large** | 6 | 6 | 8 | 128 | ~8.4M |

### Training Parameters
```python
MODEL_CONFIG = {
    'training_params': {
        'learning_rate': 1e-4,
        'batch_size': 8,
        'epochs': 200,
        'patience': 50
    },
    'vp_range': (40, 400),  # μs/ft without artificial bounds
    'sequence_length': 640,
    'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
    'improvements': {
        'sigmoid_fix': True,
        'proper_normalization': True,
        'data_leakage_prevented': True,
        'enhanced_decoder': True
    }
}
```

## Hardware Requirements

### Minimum Requirements
- **RAM**: 8GB+ system memory
- **Storage**: 2GB+ for datasets and models  
- **Python**: 3.7+ with PyTorch 1.8+

### Recommended (GPU Acceleration)
- **GPU**: CUDA-compatible with 4GB+ memory
- **Performance**: 4-5x faster training/inference vs CPU
- **CUDA**: Version 11.1+ for optimal PyTorch support

## API Reference

### Core Classes

#### VpPredictor (Simple Interface)
```python
predictor = VpPredictor(model_path, device_id=0, model_type="base")
vp_pred = predictor.predict_from_curves(gr, cnl, den, rlld)
vp_pred = predictor.predict_from_file("well_data.hdf5")
model_info = predictor.get_model_info()
```

#### VpTransformerAPI (Advanced Interface)  
```python
api = VpTransformerAPI(model_path, device="auto", model_type="base")
result = api.predict(data, format="curves", validate=True)
batch_results = api.batch_predict(data_list)
integrity_check = api.verify_model_integrity()
```

#### Data Processing Components
```python
# VpDataNormalizer - handles curve-specific normalization
normalizer = VpDataNormalizer()
normalized = normalizer.normalize_inputs(curves_dict)
vp_physical = normalizer.denormalize_vp(normalized_vp)

# LASProcessor - handles LAS/HDF5 file processing  
processor = LASProcessor()
curves = processor.process_hdf5_to_las_format("A1.hdf5")
input_features, target = processor.prepare_for_prediction(curves)
```

## Integration Examples

### External Application Integration
```python
from vp_predictor import VpPredictor

class WellLogAnalyzer:
    def __init__(self):
        self.vp_predictor = VpPredictor("models/vp_model.pth")
    
    def analyze_well(self, well_data):
        vp_prediction = self.vp_predictor.predict_from_curves(
            well_data['GR'], well_data['CNL'], 
            well_data['DEN'], well_data['RLLD']
        )
        return {'predicted_vp': vp_prediction}
```

### API Service Integration
```python
from flask import Flask, request, jsonify
from vp_predictor import VpTransformerAPI

app = Flask(__name__)
vp_api = VpTransformerAPI("models/vp_model.pth")

@app.route('/predict', methods=['POST'])
def predict_vp():
    data = request.json
    result = vp_api.predict(data, validate=True)
    return jsonify(result)
```

## Performance Expectations

### Accuracy Metrics (Improved Models)
- **RMSE**: <15 μs/ft (typical well log data)
- **R²**: >0.85 (correlation with actual Vp measurements)  
- **Range Coverage**: Full [40-400] μs/ft without artificial bounds or saturation
- **Confidence**: Prediction reliability scoring with validation warnings

### Speed Performance
- **GPU Inference**: ~0.1 seconds per 640-point sequence
- **CPU Inference**: ~0.5 seconds per 640-point sequence
- **Batch Processing**: Linear scaling with batch size for multiple wells

## Device Management

The package includes automatic GPU/CPU detection with graceful fallback:
- `get_device()`: Intelligent device selection with GPU information display
- `load_checkpoint()`: Device-aware model loading with automatic mapping
- Auto-detection handles CUDA availability and device compatibility

## Dependencies

### Required (Core Functionality)
- `torch>=1.8.0`: PyTorch deep learning framework
- `numpy>=1.19.0`: Numerical computing  
- `h5py>=3.1.0`: HDF5 file handling for well log data
- `scikit-learn>=0.24.0`: Data preprocessing and metrics

### Optional (Enhanced Features)  
- `matplotlib>=3.3.0`: Plotting and visualization
- `pandas>=1.2.0`: Data analysis and manipulation
- `lasio>=0.30`: LAS file reading (complete LAS support)
- `thop>=0.0.31`: Model profiling (FLOPs and parameter counting)

## Common Troubleshooting

### "Predictions stuck at ~40 μs/ft"
**Cause**: Using legacy model with sigmoid constraints  
**Solution**: Retrain with improved VpDecoder architecture (vp_model_improved.py)

### "Model loading fails"
**Cause**: Device mismatch or corrupted checkpoint  
**Solution**: Check device compatibility using `get_device()` and verify model integrity

### "Poor prediction range"
**Cause**: Artificial activation constraints in decoder  
**Solution**: Update to improved VpDecoder without sigmoid limitations

### Performance Optimization
- **Enable GPU**: Use `device_id=0` for GPU acceleration (4-5x speedup)
- **Batch Processing**: Use `batch_predict()` for multiple predictions
- **Memory Issues**: Reduce batch size if encountering OOM errors
- **Validation**: Use `validate=True` for input quality checks and confidence scoring

## Well Log Domain Context

This package focuses on **sonic velocity (Vp) prediction** from conventional well log measurements. The VpTransformer uses a transformer-based deep learning architecture specifically optimized for the sequential nature of well log data and the physical relationships between different petrophysical measurements.

**Key Applications:**
- Fill missing sonic log sections in wellbores
- Quality control for existing Vp measurements  
- Synthetic log generation for reservoir characterization
- Real-time logging optimization and validation
- Petrophysical analysis and formation evaluation