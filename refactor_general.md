# Refactoring Plan: VpTransformer → General Well Log Transformer

## Executive Summary

This document outlines a comprehensive refactoring plan to transform the current VpTransformer (specialized for sonic velocity prediction) into a **General Well Log Transformer (GWLT)** capable of predicting multiple well log curves from various input combinations. The refactoring will maintain backward compatibility while enabling flexible multi-curve predictions.

## Current Architecture Analysis

### Hardcoded Vp-Specific Components Identified

1. **VpDecoder** (`vp_model_improved.py:21-55`)
   - Fixed to single output channel (Vp only)
   - Hardcoded normalization assumptions
   - Vp-specific activation patterns

2. **VpDataNormalizer** (`vp_model_improved.py:113-156`)
   - Hardcoded Vp statistics: `{'mean': 200.0, 'std': 75.0, 'min': 40, 'max': 400}`
   - Single target curve normalization methods
   - Vp-specific denormalization logic

3. **VpLoss** (`vp_model_improved.py:159-176`)
   - Vp-specific physical constraints: `torch.relu(pred - 500) + torch.relu(20 - pred)`
   - Single-curve loss calculation

4. **Fixed Configuration** (`__init__.py:76-99`)
   - Hardcoded input curves: `['GR', 'CNL', 'DEN', 'RLLD']`
   - Single output curve: `'VP'`
   - Vp-specific range: `(40, 400)`

5. **VpDataset** (`vp_model_improved.py:178-208`)
   - Assumes single target (AC/Vp curve)
   - Hardcoded input/output relationship

## Refactoring Strategy

### Phase 1: Core Architecture Generalization

#### 1.1 Flexible Decoder Architecture
```python
# Current: VpDecoder (single-output)
class VpDecoder(nn.Module):
    def __init__(self, res_num=4, out_channels=1, feature_num=64):

# Refactored: GeneralDecoder (multi-output)
class GeneralDecoder(nn.Module):
    def __init__(self, res_num=4, out_channels=1, feature_num=64, 
                 output_curves=None, activation_types=None):
        """
        Args:
            out_channels: Number of output curves
            output_curves: List of curve names ['DEN', 'AC', 'PE', etc.]
            activation_types: Dict mapping curves to activation types
                             {'DEN': 'sigmoid', 'AC': 'none', 'PE': 'relu'}
        """
```

#### 1.2 Configurable Transformer Architecture
```python
# Current: VpTransformer (Vp-specific)
class VpTransformer(nn.Module):

# Refactored: GeneralWellLogTransformer (multi-curve)
class GeneralWellLogTransformer(nn.Module):
    def __init__(self, input_curves, output_curves, curve_config, **kwargs):
        """
        Args:
            input_curves: List of input curve names ['GR', 'CNL', 'DEN', 'RLLD']
            output_curves: List of output curve names ['AC', 'DEN', 'PE']
            curve_config: Configuration dict for each curve's properties
        """
```

### Phase 2: Data Pipeline Generalization

#### 2.1 Flexible Data Normalizer
```python
# Current: VpDataNormalizer (Vp-specific)
class VpDataNormalizer:

# Refactored: GeneralDataNormalizer (multi-curve)
class GeneralDataNormalizer:
    def __init__(self, curve_configs):
        """
        Args:
            curve_configs: Dict of curve configurations
            {
                'GR': {'type': 'input', 'mean': 75.0, 'std': 50.0, 'range': (0, 200)},
                'AC': {'type': 'output', 'mean': 100.0, 'std': 30.0, 'range': (40, 400)},
                'DEN': {'type': 'both', 'mean': 2.5, 'std': 0.3, 'range': (1.5, 3.0)},
                'RLLD': {'type': 'input', 'transform': 'log10', 'mean': 10.0, 'std': 50.0}
            }
        """
```

#### 2.2 Multi-Target Dataset
```python
# Current: VpDataset (single target)
class VpDataset(torch.utils.data.Dataset):

# Refactored: GeneralWellLogDataset (multi-target)
class GeneralWellLogDataset(torch.utils.data.Dataset):
    def __init__(self, data_dict, input_curves, target_curves, 
                 normalizer, sequence_config):
        """
        Args:
            data_dict: Dict containing all curve data
            input_curves: List of input curve names
            target_curves: List of target curve names  
            normalizer: GeneralDataNormalizer instance
            sequence_config: Sequence handling configuration
        """
```

### Phase 3: Configuration System

#### 3.1 Curve Configuration Schema
```python
CURVE_CONFIGURATIONS = {
    # Petrophysical curve definitions
    'GR': {
        'name': 'Gamma Ray',
        'unit': 'API',
        'type': 'input',
        'physics_range': (0, 200),
        'normalization': {'method': 'zscore', 'mean': 75.0, 'std': 50.0},
        'preprocessing': None
    },
    'AC': {
        'name': 'Acoustic',
        'unit': 'μs/ft', 
        'type': 'output',
        'physics_range': (40, 400),
        'normalization': {'method': 'zscore', 'mean': 200.0, 'std': 75.0},
        'activation': 'none'  # No artificial constraints
    },
    'DEN': {
        'name': 'Bulk Density',
        'unit': 'g/cm³',
        'type': 'both',  # Can be input or output
        'physics_range': (1.5, 3.0),
        'normalization': {'method': 'zscore', 'mean': 2.5, 'std': 0.3},
        'activation': 'sigmoid'  # Density benefits from bounds
    },
    'RLLD': {
        'name': 'Deep Resistivity', 
        'unit': 'ohm-m',
        'type': 'input',
        'physics_range': (0.1, 1000),
        'normalization': {'method': 'log_zscore', 'log_base': 10},
        'preprocessing': 'log10'
    }
}
```

#### 3.2 Model Configuration Templates
```python
MODEL_TEMPLATES = {
    'vp_prediction': {
        'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
        'output_curves': ['AC'],
        'description': 'Sonic velocity prediction (backward compatible)'
    },
    'density_prediction': {
        'input_curves': ['GR', 'CNL', 'AC', 'RLLD'], 
        'output_curves': ['DEN'],
        'description': 'Density curve prediction'
    },
    'multi_curve': {
        'input_curves': ['GR', 'CNL'],
        'output_curves': ['DEN', 'AC', 'RLLD'],
        'description': 'Multi-curve prediction from basic logs'
    },
    'missing_section': {
        'input_curves': ['GR', 'DEN', 'AC'],
        'output_curves': ['CNL', 'RLLD'], 
        'description': 'Fill missing log sections'
    }
}
```

### Phase 4: API Modernization

#### 4.1 General Predictor Interface
```python
# Current: VpPredictor (Vp-specific)
class VpPredictor:

# Refactored: GeneralWellLogPredictor (multi-curve)
class GeneralWellLogPredictor:
    def __init__(self, model_path, prediction_config, device_id=0):
        """
        Args:
            model_path: Path to trained model
            prediction_config: Configuration dict specifying input/output curves
            device_id: GPU device ID
        """
        
    def predict(self, input_data, format='curves'):
        """
        General prediction method
        Args:
            input_data: Dict of input curves or file path
            format: 'curves', 'file', 'dataframe'
        Returns:
            Dict of predicted curves
        """
        
    def predict_curves(self, **curve_data):
        """Predict from individual curve arrays"""
        
    def predict_file(self, file_path):
        """Predict from LAS/HDF5 file"""
        
    def predict_missing_sections(self, available_curves, target_curves):
        """Fill missing log sections"""
```

#### 4.2 Advanced Multi-Curve API
```python
class GeneralWellLogAPI:
    def __init__(self, model_path, curve_configs, device='auto'):
        """Advanced API with full configuration flexibility"""
        
    def train_model(self, training_config):
        """Train new model with specified configuration"""
        
    def predict_batch(self, data_list, parallel=True):
        """Batch prediction with parallel processing"""
        
    def evaluate_model(self, test_data, metrics=['rmse', 'r2', 'mae']):
        """Comprehensive model evaluation"""
        
    def get_supported_curves(self):
        """Return list of supported input/output curves"""
```

## File Structure Changes

### Current Structure
```
vp_predictor/
├── __init__.py
├── vp_model_improved.py      # Vp-specific
├── model.py                  # Base components (keep)
├── utils.py                  # Utilities (keep)
└── las_processor.py          # Data processing (expand)
```

### Refactored Structure
```
well_log_transformer/              # Renamed package
├── __init__.py                    # Updated exports
├── core/
│   ├── __init__.py
│   ├── model.py                   # Base transformer (unchanged)
│   ├── decoder.py                 # GeneralDecoder (refactored)
│   ├── normalizer.py              # GeneralDataNormalizer (new)
│   └── dataset.py                 # GeneralWellLogDataset (new)
├── configs/
│   ├── __init__.py
│   ├── curves.py                  # CURVE_CONFIGURATIONS
│   ├── models.py                  # MODEL_TEMPLATES
│   └── training.py                # Training configurations
├── api/
│   ├── __init__.py
│   ├── predictor.py               # GeneralWellLogPredictor
│   └── advanced_api.py            # GeneralWellLogAPI
├── data/
│   ├── __init__.py
│   ├── processor.py               # Enhanced LASProcessor
│   └── validators.py              # Data validation
├── legacy/
│   ├── __init__.py
│   └── vp_specific.py             # Backward compatibility
└── utils/                         # Enhanced utilities
    ├── __init__.py
    ├── device_utils.py
    ├── loss_functions.py
    └── evaluation.py
```

## Implementation Phases

### Phase 1: Foundation (Weeks 1-2)
1. **Create new package structure**
2. **Implement GeneralDecoder with configurable outputs**
3. **Develop GeneralDataNormalizer with flexible curve handling**
4. **Create curve configuration system**

### Phase 2: Core Functionality (Weeks 3-4) 
1. **Implement GeneralWellLogTransformer**
2. **Develop GeneralWellLogDataset for multi-target training**
3. **Create flexible loss functions for different curve types**
4. **Build configuration templates for common use cases**

### Phase 3: API Development (Weeks 5-6)
1. **Implement GeneralWellLogPredictor with backward compatibility**
2. **Develop advanced API with batch processing**
3. **Create comprehensive validation and error handling**
4. **Build evaluation and metrics framework**

### Phase 4: Testing & Documentation (Weeks 7-8)
1. **Comprehensive testing suite for all configurations**
2. **Performance benchmarking vs specialized models**
3. **Complete API documentation and examples**
4. **Migration guide for existing VpTransformer users**

## Backward Compatibility Strategy

### Legacy Support Module
```python
# legacy/vp_specific.py
class VpPredictor:
    """Backward compatible VpPredictor interface"""
    def __init__(self, *args, **kwargs):
        # Wrapper around GeneralWellLogPredictor with Vp config
        vp_config = MODEL_TEMPLATES['vp_prediction']
        self._predictor = GeneralWellLogPredictor(prediction_config=vp_config)
        
    def predict_from_curves(self, gr, cnl, den, rlld):
        # Maintains exact same interface
        return self._predictor.predict({
            'GR': gr, 'CNL': cnl, 'DEN': den, 'RLLD': rlld
        })['AC']
```

### Import Compatibility
```python
# __init__.py - maintains old imports
from .legacy.vp_specific import VpPredictor  # Backward compatibility
from .api.predictor import GeneralWellLogPredictor  # New functionality
```

## Configuration Examples

### Example 1: Vp Prediction (Backward Compatible)
```python
config = {
    'model_type': 'vp_prediction',
    'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
    'output_curves': ['AC'],
    'training_params': {
        'learning_rate': 1e-4,
        'batch_size': 8,
        'epochs': 200
    }
}
```

### Example 2: Multi-Curve Prediction
```python
config = {
    'model_type': 'custom',
    'input_curves': ['GR', 'DEN'],
    'output_curves': ['AC', 'CNL', 'RLLD'],
    'curve_weights': {'AC': 1.0, 'CNL': 0.8, 'RLLD': 0.6},
    'training_params': {
        'learning_rate': 5e-5,
        'batch_size': 16,
        'epochs': 300
    }
}
```

### Example 3: Missing Section Filling
```python
config = {
    'model_type': 'section_filling',
    'available_curves': ['GR', 'DEN', 'AC'],
    'missing_curves': ['CNL', 'RLLD'],
    'prediction_mode': 'interpolation',
    'confidence_threshold': 0.8
}
```

## Migration Guide

### For Existing VpTransformer Users

#### Step 1: Update Imports (No Code Changes)
```python
# Old (still works)
from vp_predictor import VpPredictor

# New (recommended)
from well_log_transformer import GeneralWellLogPredictor
from well_log_transformer.configs import MODEL_TEMPLATES
```

#### Step 2: Migrate to General API (Optional)
```python
# Old VpPredictor usage
vp_predictor = VpPredictor("model.pth")
vp_result = vp_predictor.predict_from_curves(gr, cnl, den, rlld)

# New GeneralWellLogPredictor usage
config = MODEL_TEMPLATES['vp_prediction']
predictor = GeneralWellLogPredictor("model.pth", config)
results = predictor.predict({'GR': gr, 'CNL': cnl, 'DEN': den, 'RLLD': rlld})
vp_result = results['AC']
```

## Testing Strategy

### Unit Tests
1. **Decoder flexibility tests** - various output configurations
2. **Normalizer tests** - different curve types and transformations  
3. **Dataset tests** - multi-target, missing data handling
4. **Configuration validation** - invalid configs, edge cases

### Integration Tests
1. **End-to-end prediction workflows** for each template
2. **Backward compatibility** - existing VpPredictor code
3. **Performance benchmarks** - speed vs specialized models
4. **Memory usage** - multi-output vs single-output models

### Validation Tests
1. **Geological validity** - predicted curves within physical ranges
2. **Cross-curve consistency** - relationships between predicted curves
3. **Model robustness** - noisy inputs, missing data sections

## Performance Considerations

### Memory Optimization
- **Shared encoder** - same feature extraction for multiple outputs
- **Selective decoding** - only compute requested output curves
- **Batch processing** - efficient multi-curve prediction

### Computational Efficiency
- **Dynamic model loading** - load only required decoder heads
- **Cached normalizers** - reuse normalization parameters
- **Parallel prediction** - concurrent processing for independent curves

### Model Size Management
- **Modular checkpoints** - separate encoder/decoder weights
- **Compression techniques** - model pruning for deployment
- **Progressive loading** - load components as needed

## Risk Mitigation

### Technical Risks
1. **Performance degradation** - Monitor vs specialized models
2. **Memory consumption** - Multi-output models require more RAM
3. **Training complexity** - Multi-target optimization challenges

### Mitigation Strategies
1. **Specialized variants** - Keep optimized single-curve models
2. **Progressive rollout** - Phase introduction of new features
3. **Comprehensive testing** - Extensive validation before release

## Success Metrics

### Functionality Metrics
- ✅ Support for 5+ common well log curves
- ✅ Backward compatibility maintained (100% VpPredictor API)
- ✅ <10% performance degradation vs specialized models

### Usability Metrics  
- ✅ Configuration-driven predictions (no hardcoding)
- ✅ 50% reduction in code for multi-curve scenarios
- ✅ Comprehensive documentation and examples

### Performance Metrics
- ✅ Memory usage <2x single-curve models
- ✅ Training time <3x single-curve models
- ✅ Prediction accuracy within 5% of specialized models

## Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Foundation | 2 weeks | GeneralDecoder, DataNormalizer, Config system |
| Core | 2 weeks | GeneralTransformer, Dataset, Loss functions |
| API | 2 weeks | Predictor APIs, Validation, Batch processing |
| Testing | 2 weeks | Test suite, Documentation, Migration guide |
| **Total** | **8 weeks** | **Production-ready General Well Log Transformer** |

This refactoring plan provides a roadmap to transform the specialized VpTransformer into a flexible, general-purpose well log prediction system while maintaining full backward compatibility and achieving the goal of supporting multiple well log curve predictions from various input combinations.