"""
Improved model architecture specifically for Vp (Sonic Velocity) prediction
Addresses the scale mismatch and activation function issues
Complete module with training, evaluation, and inference capabilities
"""
import os
import argparse
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import h5py
from sklearn.model_selection import train_test_split

from model import Input_Embedding, PositionalEncoding, TransformerBlock, ResCNN
from utils import get_device, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from las_processor import LASProcessor

class VpDecoder(nn.Module):
    """
    Decoder specifically designed for Vp prediction
    Outputs normalized values that are denormalized during inference
    FIXED: Removed sigmoid activation to eliminate artificial bounds constraints
    """
    def __init__(self, res_num=4, out_channels=1, feature_num=64):
        super().__init__()
        self.rescnn = nn.ModuleList([
            ResCNN(in_channels=feature_num, out_channels=feature_num,
                   kernel_size=11, padding=5, stride=1)
            for i in range(res_num)
        ])

        # Improved output layer without hard-coded range constraints
        self.out_layer = nn.Sequential(
            nn.Conv1d(in_channels=feature_num, out_channels=feature_num//2,
                     kernel_size=11, padding=5, stride=1),
            nn.ReLU(),
            nn.Conv1d(in_channels=feature_num//2, out_channels=out_channels,
                     kernel_size=11, padding=5, stride=1)
            # REMOVED: ReLU and Linear layers that force artificial constraints
            # REMOVED: Hard-coded Vp range scaling parameters
        )

    def forward(self, x):
        for model in self.rescnn:
            x = model(x)

        x = self.out_layer(x)

        # SOLUTION: Output normalized values, denormalize during inference
        # This allows the model to learn the proper range naturally without
        # artificial constraints from sigmoid saturation
        return x  # Returns normalized Vp values

class VpTransformer(nn.Module):
    """
    Complete transformer model for Vp prediction with proper scaling
    """
    def __init__(self, in_channels=4, out_channels=1, feature_num=64, res_num=4, 
                 encoder_num=4, use_pe=True, dim=64, seq_len=640, num_heads=4, 
                 mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0., position_drop=0.,
                 act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        
        self.feature_embedding = Input_Embedding(in_channels=in_channels, 
                                                feature_num=feature_num, res_num=res_num)
        self.position_embedding = PositionalEncoding(d_model=dim, dropout=position_drop, 
                                                    seq_len=seq_len)
        self.use_pe = use_pe
        self.transformer_encoder = TransformerBlock(block_num=encoder_num, dim=dim, 
                                                   num_heads=num_heads, mlp_ratio=mlp_ratio, 
                                                   qkv_bias=qkv_bias, drop=drop, attn_drop=attn_drop,
                                                   act_layer=act_layer, norm_layer=norm_layer)
        
        # Use Vp-specific decoder
        self.decoder = VpDecoder(out_channels=out_channels, feature_num=feature_num, 
                                res_num=res_num)

    def forward(self, x):
        # [B,in_channels,L] --> [B,feature_num,L]
        x = self.feature_embedding(x)
        # [B,feature_num,L]--> [B,L,feature_num]
        x = x.transpose(-2, -1)
        if self.use_pe:
            x = self.position_embedding(x)
        # [B, L, feature_num] --> [B, L, feature_num]
        x = self.transformer_encoder(x)
        # [B,  L, feature_num] --> [B, feature_num,L]
        x = x.transpose(-2, -1)
        x = self.decoder(x)
        return x

def MWLT_Vp_Small(in_channels=4, out_channels=1, feature_num=64, **kwargs):
    """Small Vp-specific transformer model"""
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=2, encoder_num=2, 
                        num_heads=2, **kwargs)

def MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64, **kwargs):
    """Base Vp-specific transformer model"""
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=4, encoder_num=4, 
                        num_heads=4, **kwargs)

def MWLT_Vp_Large(in_channels=4, out_channels=1, feature_num=128, **kwargs):
    """Large Vp-specific transformer model"""
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=6, encoder_num=6, 
                        num_heads=8, **kwargs)

class VpDataNormalizer:
    """
    Proper data normalization for Vp prediction
    """
    def __init__(self):
        # Input curve normalization parameters
        self.input_stats = {
            'GR': {'mean': 75.0, 'std': 50.0, 'min': 0, 'max': 200},
            'CNL': {'mean': 20.0, 'std': 15.0, 'min': 0, 'max': 60},
            'DEN': {'mean': 2.5, 'std': 0.3, 'min': 1.5, 'max': 3.0},
            'RLLD': {'mean': 10.0, 'std': 50.0, 'min': 0.1, 'max': 1000}  # Log scale
        }
        
        # Vp normalization parameters
        self.vp_stats = {'mean': 200.0, 'std': 75.0, 'min': 40, 'max': 400}
    
    def normalize_inputs(self, curves_dict):
        """Normalize input curves to [-1, 1] range"""
        normalized = {}
        
        for curve_name, data in curves_dict.items():
            if curve_name in self.input_stats:
                stats = self.input_stats[curve_name]
                
                if curve_name == 'RLLD':
                    # Log transform for resistivity
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized[curve_name] = (data - 1.0) / 2.0  # Normalize log values
                else:
                    # Standard normalization
                    normalized[curve_name] = (data - stats['mean']) / stats['std']
                    normalized[curve_name] = torch.clamp(normalized[curve_name], -3, 3) / 3
            else:
                normalized[curve_name] = data
                
        return normalized
    
    def normalize_vp(self, vp_data):
        """Normalize Vp values for training"""
        return (vp_data - self.vp_stats['mean']) / self.vp_stats['std']
    
    def denormalize_vp(self, normalized_vp):
        """Convert normalized Vp back to physical units"""
        return normalized_vp * self.vp_stats['std'] + self.vp_stats['mean']

# Loss function specifically for Vp prediction
class VpLoss(nn.Module):
    """
    Custom loss function for Vp prediction that handles the physical constraints
    """
    def __init__(self, alpha=1.0, beta=0.1):
        super().__init__()
        self.mse_loss = nn.MSELoss()
        self.alpha = alpha  # Weight for MSE loss
        self.beta = beta    # Weight for physical constraint loss
        
    def forward(self, pred, target):
        # Standard MSE loss
        mse = self.mse_loss(pred, target)
        
        # Physical constraint loss (penalize unrealistic values)
        constraint_loss = torch.mean(torch.relu(pred - 500) + torch.relu(20 - pred))
        
        return self.alpha * mse + self.beta * constraint_loss

class VpDataset(torch.utils.data.Dataset):
    """
    Improved dataset for Vp prediction with proper normalization
    """
    def __init__(self, input_data, target_data, normalizer, total_seqlen=720, effect_seqlen=640, transform=False):
        self.input_data = input_data
        self.target_data = target_data
        self.normalizer = normalizer
        self.total_seqlen = total_seqlen
        self.effect_seqlen = effect_seqlen
        self.transform = transform
        
    def __len__(self):
        return len(self.input_data)
    
    def __getitem__(self, idx):
        # Get input curves and target
        inputs = self.input_data[idx]  # Shape: [4, 720] for GR, CNL, DEN, RLLD
        target = self.target_data[idx]  # Shape: [720] for AC
        
        # Apply data augmentation if requested
        if self.transform and self.total_seqlen > self.effect_seqlen:
            start_idx = np.random.randint(0, self.total_seqlen - self.effect_seqlen + 1)
            inputs = inputs[:, start_idx:start_idx + self.effect_seqlen]
            target = target[start_idx:start_idx + self.effect_seqlen]
        else:
            inputs = inputs[:, :self.effect_seqlen]
            target = target[:self.effect_seqlen]
        
        return torch.FloatTensor(inputs), torch.FloatTensor(target).unsqueeze(0)

def find_data_files():
    """
    Auto-detect the location of A1.hdf5 and A2.hdf5 files
    """
    # Possible locations to search
    possible_paths = [
        # Current directory and parent
        '.',
        '..',
        # Your specific path
        r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        # Relative paths from code directory
        os.path.join('..'),
        os.path.join('..', '..'),
    ]

    found_files = []
    for base_path in possible_paths:
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')

        if os.path.exists(a1_path) and os.path.exists(a2_path):
            found_files = [a1_path, a2_path]
            print(f"Found data files in: {os.path.abspath(base_path)}")
            break

    if not found_files:
        print("Could not find A1.hdf5 and A2.hdf5 files!")
        print("Searched in:")
        for path in possible_paths:
            print(f"  - {os.path.abspath(path)}")
        print("\nPlease ensure A1.hdf5 and A2.hdf5 are in one of these locations.")

    return found_files

def create_improved_vp_data():
    """
    Create improved training data with proper normalization and augmentation
    """
    processor = LASProcessor()
    normalizer = VpDataNormalizer()

    print("Creating improved Vp training data...")

    # Auto-detect data file locations
    input_files = find_data_files()
    if not input_files:
        return np.array([]), np.array([])
    all_inputs = []
    all_targets = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found, skipping...")
            continue
            
        print(f"Processing {file_path}...")
        curves = processor.process_hdf5_to_las_format(file_path)
        
        # Create multiple overlapping windows for data augmentation
        sequence_length = 720
        step_size = 360  # 50% overlap
        
        data_length = len(curves['AC'])
        num_windows = max(1, (data_length - sequence_length) // step_size + 1)
        
        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + sequence_length
            
            if end_idx > data_length:
                start_idx = data_length - sequence_length
                end_idx = data_length
            
            # Extract window
            window_curves = {}
            for curve_name, data in curves.items():
                window_curves[curve_name] = data[start_idx:end_idx]
            
            # Normalize inputs
            input_features = []
            for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
                if curve_name in window_curves:
                    data = torch.FloatTensor(window_curves[curve_name])
                    if curve_name == 'RLLD':
                        # Log transform for resistivity
                        data = torch.log10(torch.clamp(data, min=0.1))
                        normalized = (data - 1.0) / 2.0
                    else:
                        stats = normalizer.input_stats[curve_name]
                        normalized = (data - stats['mean']) / stats['std']
                        normalized = torch.clamp(normalized, -3, 3) / 3
                    input_features.append(normalized.numpy())
                else:
                    input_features.append(np.zeros(sequence_length))
            
            # Target (AC) - keep in original units for now
            target = window_curves.get('AC', np.zeros(sequence_length))
            
            all_inputs.append(np.array(input_features))
            all_targets.append(target)
    
    print(f"Created {len(all_inputs)} training samples")
    return np.array(all_inputs), np.array(all_targets)